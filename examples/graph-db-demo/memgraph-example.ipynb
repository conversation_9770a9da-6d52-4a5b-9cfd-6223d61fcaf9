#%% md
# Memgraph as Graph Memory
#%% md
## Prerequisites

### 1. Install Mem0 with Graph Memory support 

To use Mem0 with Graph Memory support, install it using pip:

```bash
pip install "mem0ai[graph]"
```

This command installs Mem0 along with the necessary dependencies for graph functionality.

### 2. Install Memgraph

To utilize Memgraph as Graph Memory, run it with Docker:

```bash
docker run -p 7687:7687 memgraph/memgraph-mage:latest --schema-info-enabled=True
```

The `--schema-info-enabled` flag is set to `True` for more performant schema
generation.

Additional information can be found on [Memgraph documentation](https://memgraph.com/docs). 
#%% md
## Configuration

Do all the imports and configure OpenAI (enter your OpenAI API key):
#%%
from mem0 import Memory

import os

os.environ["OPENAI_API_KEY"] = ""
#%% md
Set up configuration to use the embedder model and Memgraph as a graph store:
#%%
config = {
    "embedder": {
        "provider": "openai",
        "config": {"model": "text-embedding-3-large", "embedding_dims": 1536},
    },
    "graph_store": {
        "provider": "memgraph",
        "config": {
            "url": "bolt://localhost:7687",
            "username": "memgraph",
            "password": "mem0graph",
        },
    },
}
#%% md
## Graph Memory initializiation 

Initialize Memgraph as a Graph Memory store: 
#%%
m = Memory.from_config(config_dict=config)
#%% md
## Store memories 

Create memories:
#%%
messages = [
    {
        "role": "user",
        "content": "I'm planning to watch a movie tonight. Any recommendations?",
    },
    {
        "role": "assistant",
        "content": "How about a thriller movies? They can be quite engaging.",
    },
    {
        "role": "user",
        "content": "I'm not a big fan of thriller movies but I love sci-fi movies.",
    },
    {
        "role": "assistant",
        "content": "Got it! I'll avoid thriller recommendations and suggest sci-fi movies in the future.",
    },
]
#%% md
Store memories in Memgraph:
#%%
# Store inferred memories (default behavior)
result = m.add(messages, user_id="alice", metadata={"category": "movie_recommendations"})
#%% md
![](./alice-memories.png)
#%% md
## Search memories
#%%
for result in m.search("what does alice love?", user_id="alice")["results"]:
    print(result["memory"], result["score"])