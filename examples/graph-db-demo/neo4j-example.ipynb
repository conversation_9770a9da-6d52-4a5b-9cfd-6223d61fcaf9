#%% md
# Neo4j as Graph Memory
#%% md
## Prerequisites

### 1. Install Mem0 with Graph Memory support

To use Mem0 with Graph Memory support, install it using pip:

```bash
pip install "mem0ai[graph]"
```

This command installs Mem0 along with the necessary dependencies for graph functionality.

### 2. Install Neo4j

To utilize Neo4j as Graph Memory, run it with Docker:

```bash
docker run \
  -p 7474:7474 -p 7687:7687 \
  -e NEO4J_AUTH=neo4j/password \
  neo4j:5
```

This command starts Neo4j with default credentials (`neo4j` / `password`) and exposes both the HTTP (7474) and Bolt (7687) ports.

You can access the Neo4j browser at [http://localhost:7474](http://localhost:7474).

Additional information can be found in the [Neo4j documentation](https://neo4j.com/docs/).

#%% md
## Configuration

Do all the imports and configure OpenAI (enter your OpenAI API key):
#%%
from mem0 import Memory

import os

os.environ["OPENAI_API_KEY"] = (
    ""
)
#%% md
Set up configuration to use the embedder model and Neo4j as a graph store:
#%%
config = {
    "embedder": {
        "provider": "openai",
        "config": {"model": "text-embedding-3-large", "embedding_dims": 1536},
    },
    "graph_store": {
        "provider": "neo4j",
        "config": {
            "url": "bolt://*************:7687",
            "username": "neo4j",
            "password": "causes-bins-vines",
        },
    },
}
#%% md
## Graph Memory initializiation

Initialize Neo4j as a Graph Memory store:
#%%
m = Memory.from_config(config_dict=config)
#%% md
## Store memories

Create memories:
#%%
messages = [
    {
        "role": "user",
        "content": "I'm planning to watch a movie tonight. Any recommendations?",
    },
    {
        "role": "assistant",
        "content": "How about a thriller movies? They can be quite engaging.",
    },
    {
        "role": "user",
        "content": "I'm not a big fan of thriller movies but I love sci-fi movies.",
    },
    {
        "role": "assistant",
        "content": "Got it! I'll avoid thriller recommendations and suggest sci-fi movies in the future.",
    },
]

#%% md
Store memories in Neo4j:
#%%
# Store inferred memories (default behavior)
result = m.add(
    messages, user_id="alice"
)
#%% md
![](https://github.com/tomasonjo/mem0/blob/neo4jexample/examples/graph-db-demo/alice-memories.png?raw=1)
#%% md
## Search memories
#%%
for result in m.search("what does alice love?", user_id="alice")["results"]:
    print(result["memory"], result["score"])
#%%
