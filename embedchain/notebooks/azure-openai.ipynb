#%% md
## Cookbook for using Azure OpenAI with Embedchain
#%% md
### Step-1: Install embedchain package
#%%
!pip install embedchain
#%% md
### Step-2: Set Azure OpenAI related environment variables

You can find these env variables on your Azure OpenAI dashboard.
#%%
import os
from embedchain import App

os.environ["OPENAI_API_TYPE"] = "azure"
os.environ["OPENAI_API_BASE"] = "https://xxx.openai.azure.com/"
os.environ["OPENAI_API_KEY"] = "xxx"
os.environ["OPENAI_API_VERSION"] = "xxx"
#%% md
### Step-3: Define your llm and embedding model config
#%%
config = """
llm:
  provider: azure_openai
  model: gpt-35-turbo
  config:
    deployment_name: ec_openai_azure
    temperature: 0.5
    max_tokens: 1000
    top_p: 1
    stream: false

embedder:
  provider: azure_openai
  config:
    model: text-embedding-ada-002
    deployment_name: ec_embeddings_ada_002
"""

# Write the multi-line string to a YAML file
with open('azure_openai.yaml', 'w') as file:
    file.write(config)
#%% md
### Step-4 Create embedchain app based on the config
#%%
app = App.from_config(config_path="azure_openai.yaml")
#%% md
### Step-5: Add data sources to your app
#%%
app.add("https://www.forbes.com/profile/elon-musk")
#%% md
### Step-6: All set. Now start asking questions related to your data
#%%
while(True):
    question = input("Enter question: ")
    if question in ['q', 'exit', 'quit']:
        break
    answer = app.query(question)
    print(answer)