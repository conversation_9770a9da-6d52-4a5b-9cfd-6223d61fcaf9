#%% md
## Cookbook for using OpenAI with Embedchain
#%% md
### Step-1: Install embedchain package
#%%
!pip install embedchain
#%% md
### Step-2: Set OpenAI environment variables

You can find this env variable on your [OpenAI dashboard](https://platform.openai.com/account/api-keys).
#%%
import os
from embedchain import App

os.environ["OPENAI_API_KEY"] = "sk-xxx"
#%% md
### Step-3 Create embedchain app and define your config
#%%
app = App.from_config(config={
    "llm": {
        "provider": "openai",
        "config": {
            "model": "gpt-4o-mini",
            "temperature": 0.5,
            "max_tokens": 1000,
            "top_p": 1,
            "stream": False
        }
    },
    "embedder": {
        "provider": "openai",
        "config": {
            "model": "text-embedding-ada-002"
        }
    }
})
#%% md
### Step-4: Add data sources to your app
#%%
app.add("https://www.forbes.com/profile/elon-musk")
#%% md
### Step-5: All set. Now start asking questions related to your data
#%%
while(True):
    question = input("Enter question: ")
    if question in ['q', 'exit', 'quit']:
        break
    answer = app.query(question)
    print(answer)