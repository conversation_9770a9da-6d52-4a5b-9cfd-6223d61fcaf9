# 🧠 Mem0-DeepSeek 本地智能记忆系统

<p align="center">
  <img src="docs/images/banner-sm.png" width="800px" alt="Mem0-DeepSeek - 基于DeepSeek的本地智能记忆系统">
</p>

<p align="center">
  <strong>🚀 基于 DeepSeek + Ollama 的高性能本地个人记忆系统</strong>
</p>

<p align="center">
  <strong>✨ 完全本地化 • 🔒 数据安全 • 💰 成本可控 • 🎯 高性能</strong>
</p>

---

## ✨ 系统特色

### 🎯 **完全本地化架构**
- **LLM**: DeepSeek API (高性能推理模型)
- **嵌入模型**: Ollama + BGE-M3 (本地1024维向量)
- **向量数据库**: ChromaDB (本地持久化存储)
- **数据安全**: 所有记忆数据完全本地存储，保护隐私

### 🚀 **核心功能特性**
- 🧠 **智能记忆提取**: 自动从对话中提取关键信息
- 🔍 **语义搜索**: 基于向量相似度的智能搜索
- 👥 **多用户支持**: 支持用户隔离和权限管理
- 📝 **完整CRUD**: 添加、查询、更新、删除记忆
- 🌐 **REST API**: 完整的HTTP API接口
- 📊 **系统监控**: 实时健康检查和状态监控
- 🔄 **记忆历史**: 完整的记忆变更历史追踪

### 💡 **技术优势**
- **高性能**: 异步架构，支持并发处理
- **低成本**: 只有LLM调用产生费用，嵌入和存储完全免费
- **高可用**: 基于成熟的mem0架构，稳定可靠
- **易扩展**: 模块化设计，支持自定义扩展
- **开发友好**: 详细的API文档和示例代码

---

## 🚀 快速开始

### 📋 环境要求

- **Python**: 3.8+ 版本
- **内存**: 8GB+ 推荐
- **存储**: 10GB+ 可用空间
- **Ollama**: 用于本地嵌入模型
- **DeepSeek API**: 需要有效的API密钥

### ⚡ 一键启动

1. **克隆项目**
```bash
git clone <repository-url>
cd mem0-m
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **启动Ollama服务**
```bash
# 启动Ollama服务
ollama serve

# 下载BGE-M3嵌入模型
ollama pull bge-m3
```

4. **启动系统**
```bash
python deepseek_server.py
```

### 🌐 访问服务

启动成功后，可通过以下地址访问：

- **🏠 API服务**: http://localhost:8000
- **📚 API文档**: http://localhost:8000/docs
- **🔍 健康检查**: http://localhost:8000/health

---

## 💻 使用示例

### 🐍 Python SDK 使用

```python
from mem0 import Memory

# 初始化记忆系统
config = {
    "llm": {
        "provider": "deepseek",
        "config": {
            "model": "deepseek-chat",
            "api_key": "sk-your-deepseek-api-key",
            "deepseek_base_url": "https://api.deepseek.com",
            "temperature": 0.1,
            "max_tokens": 2000
        }
    },
    "embedder": {
        "provider": "ollama", 
        "config": {
            "model": "bge-m3",
            "ollama_base_url": "http://localhost:11434",
            "embedding_dims": 1024
        }
    },
    "vector_store": {
        "provider": "chroma",
        "config": {
            "collection_name": "mem0_deepseek",
            "path": "./chroma_db"
        }
    }
}

memory = Memory.from_config(config)

# 添加记忆
messages = [
    {"role": "user", "content": "我叫张三，今年30岁，是一名软件工程师，住在北京。我喜欢编程和阅读技术书籍。"},
    {"role": "assistant", "content": "很高兴认识您，张三！我已经记住了您的基本信息。"}
]
result = memory.add(messages, user_id="user_001")
print(f"✅ 添加了 {len(result['results'])} 个记忆")

# 搜索记忆
search_results = memory.search("张三的职业是什么？", user_id="user_001")
print("🔍 搜索结果:", search_results)

# 获取所有记忆
all_memories = memory.get_all(user_id="user_001")
print("📋 所有记忆:", all_memories)

# 更新记忆
memory.update(memory_id="memory_id", data="张三现在在学习AI技术")

# 删除记忆
memory.delete(memory_id="memory_id")
```

### 🌐 REST API 使用

#### 添加记忆
```bash
curl -X POST "http://localhost:8000/v1/memories" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {"role": "user", "content": "我喜欢喝咖啡，特别是拿铁。我每天早上都会喝一杯。"},
      {"role": "assistant", "content": "我记住了您的咖啡偏好！"}
    ],
    "user_id": "user_001"
  }'
```

#### 搜索记忆
```bash
curl -X POST "http://localhost:8000/v1/memories/search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "用户喜欢什么饮品？",
    "user_id": "user_001",
    "limit": 5
  }'
```

#### 获取所有记忆
```bash
curl "http://localhost:8000/v1/memories?user_id=user_001&limit=10"
```

#### 更新记忆
```bash
curl -X PUT "http://localhost:8000/v1/memories/{memory_id}" \
  -H "Content-Type: application/json" \
  -d '{
    "data": "用户现在更喜欢喝美式咖啡"
  }'
```

#### 删除记忆
```bash
curl -X DELETE "http://localhost:8000/v1/memories/{memory_id}"
```

---

## 🔧 详细配置

### 🤖 DeepSeek LLM 配置

```python
llm_config = {
    "provider": "deepseek",
    "config": {
        "model": "deepseek-chat",           # 模型名称
        "api_key": "sk-xxx",               # API密钥
        "deepseek_base_url": "https://api.deepseek.com",  # API地址
        "temperature": 0.1,                # 温度参数 (0.0-1.0)
        "max_tokens": 2000,               # 最大token数
        "top_p": 0.9,                     # Top-p参数
        "timeout": 120                    # 请求超时时间(秒)
    }
}
```

### 🔗 Ollama 嵌入模型配置

```python
embedder_config = {
    "provider": "ollama",
    "config": {
        "model": "bge-m3",                # 嵌入模型名称
        "ollama_base_url": "http://localhost:11434",  # Ollama服务地址
        "embedding_dims": 1024            # 向量维度
    }
}
```

### 💾 ChromaDB 向量数据库配置

```python
vector_store_config = {
    "provider": "chroma",
    "config": {
        "collection_name": "mem0_deepseek",  # 集合名称
        "path": "./chroma_db",               # 本地存储路径
        "distance_metric": "cosine"          # 距离度量方式
    }
}
```

---

## 📊 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │   REST API      │    │  Memory Core    │
│                 │◄──►│                 │◄──►│                 │
│  - 用户界面      │    │  - FastAPI      │    │  - 记忆管理      │
│  - API调用      │    │  - 路由处理      │    │  - 智能提取      │
│  - 实时交互      │    │  - 请求验证      │    │  - 上下文理解    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐             │
                       │   DeepSeek LLM  │◄────────────┤
                       │                 │             │
                       │  - 记忆提取      │             │
                       │  - 智能分析      │             │
                       │  - 上下文理解    │             │
                       └─────────────────┘             │
                                                        │
┌─────────────────┐    ┌─────────────────┐             │
│   ChromaDB      │◄──►│ Ollama+BGE-M3   │◄────────────┘
│                 │    │                 │
│  - 向量存储      │    │  - 嵌入生成      │
│  - 相似度搜索    │    │  - 本地推理      │
│  - 持久化存储    │    │  - 多语言支持    │
└─────────────────┘    └─────────────────┘
```

---

## 🔍 完整API接口文档

### 核心端点列表

| 方法 | 端点 | 描述 | 参数 |
|------|------|------|------|
| GET | `/` | 系统信息 | 无 |
| GET | `/health` | 健康检查 | 无 |
| POST | `/v1/memories` | 添加记忆 | messages, user_id, metadata |
| POST | `/v1/memories/search` | 搜索记忆 | query, user_id, limit |
| GET | `/v1/memories` | 获取所有记忆 | user_id, limit |
| PUT | `/v1/memories/{memory_id}` | 更新记忆 | memory_id, data |
| DELETE | `/v1/memories/{memory_id}` | 删除记忆 | memory_id |
| DELETE | `/v1/memories` | 删除所有记忆 | user_id |
| GET | `/v1/memories/{memory_id}/history` | 获取记忆历史 | memory_id |

### 请求响应示例

#### 添加记忆响应
```json
{
  "results": [
    {
      "id": "mem_001",
      "memory": "Name is 张三",
      "event": "ADD"
    },
    {
      "id": "mem_002", 
      "memory": "Age is 30",
      "event": "ADD"
    },
    {
      "id": "mem_003",
      "memory": "Is a software engineer",
      "event": "ADD"
    }
  ]
}
```

#### 搜索记忆响应
```json
{
  "results": [
    {
      "id": "mem_003",
      "memory": "Is a software engineer",
      "score": 0.95,
      "metadata": null,
      "created_at": "2025-01-26T10:30:00Z",
      "user_id": "user_001"
    }
  ]
}
```

### 交互式API文档

详细的API文档和交互式测试界面请访问：**http://localhost:8000/docs**

---

## 🛠️ 故障排除指南

### 常见问题解决

#### 1. Ollama连接失败
```bash
# 检查Ollama服务状态
ollama list

# 重启Ollama服务
ollama serve

# 检查端口是否被占用
netstat -ano | findstr :11434  # Windows
lsof -i :11434                 # Linux/macOS
```

#### 2. BGE-M3模型未找到
```bash
# 下载BGE-M3模型
ollama pull bge-m3

# 验证模型安装
ollama list | grep bge-m3
```

#### 3. DeepSeek API错误
- ✅ 检查API密钥是否正确
- ✅ 确认网络连接正常
- ✅ 查看API配额是否充足
- ✅ 检查API地址是否正确

#### 4. 端口被占用
```bash
# Windows查看端口占用
netstat -ano | findstr :8000

# Linux/macOS查看端口占用
lsof -i :8000

# 修改端口配置
export WEB_PORT=8001  # Linux/macOS
$env:WEB_PORT=8001    # Windows PowerShell
```

#### 5. 内存不足
- 关闭其他占用内存的程序
- 增加系统虚拟内存
- 减少并发请求数量
- 优化ChromaDB配置

### 日志分析

系统运行时会输出详细的日志信息：

```bash
# 系统启动日志
2025-01-26 10:30:00 - INFO - 🚀 初始化mem0 Memory实例...
2025-01-26 10:30:03 - INFO - ✅ Memory实例初始化成功

# API请求日志
2025-01-26 10:30:15 - INFO - 添加记忆请求: user_id=user_001
2025-01-26 10:30:16 - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-01-26 10:30:20 - INFO - 记忆添加成功: 3 个记忆

# 搜索请求日志
2025-01-26 10:30:25 - INFO - 搜索记忆请求: query='用户职业', user_id=user_001
2025-01-26 10:30:26 - INFO - 搜索完成: 找到 5 个结果
```

---

## 🔒 安全与隐私保护

### 🛡️ 数据安全措施
- **本地存储**: 所有记忆数据存储在本地ChromaDB，不上传到外部服务
- **用户隔离**: 支持多用户数据完全隔离，防止数据泄露
- **API安全**: 支持CORS配置和请求验证
- **传输加密**: 与DeepSeek API通信使用HTTPS加密

### 🔐 隐私保护特性
- **无外部依赖**: 除LLM外，所有处理都在本地完成
- **数据不上传**: 向量和记忆数据不会上传到外部服务
- **完全控制**: 用户对所有数据拥有完全控制权
- **可审计**: 完整的操作日志，支持数据审计

### 🔑 安全配置建议
```python
# 安全配置示例
security_config = {
    "api_key_encryption": True,      # API密钥加密存储
    "user_isolation": True,          # 用户数据隔离
    "audit_logging": True,           # 审计日志
    "cors_origins": ["localhost"],   # CORS限制
    "rate_limiting": {               # 请求限制
        "requests_per_minute": 60,
        "requests_per_hour": 1000
    }
}
```

---

## 📈 性能优化指南

### ⚡ 系统性能指标
- **API响应时间**: < 1秒（不含LLM调用）
- **LLM调用时间**: 5-15秒（取决于DeepSeek响应）
- **嵌入生成时间**: 1-3秒（本地Ollama）
- **向量搜索时间**: < 100ms（ChromaDB）
- **并发支持**: 支持多用户并发访问

### 🚀 性能优化建议

#### 硬件优化
```bash
# 推荐硬件配置
CPU: 8核心以上
内存: 16GB以上
存储: SSD固态硬盘
网络: 稳定的互联网连接（用于DeepSeek API）
```

#### 软件优化
```python
# 性能优化配置
performance_config = {
    "chroma_settings": {
        "persist_directory": "./chroma_db",
        "collection_metadata": {"hnsw:space": "cosine"},
        "batch_size": 100
    },
    "ollama_settings": {
        "num_ctx": 2048,
        "num_batch": 512,
        "num_gpu": 1  # 如果有GPU
    },
    "api_settings": {
        "max_concurrent_requests": 10,
        "timeout": 120,
        "retry_attempts": 3
    }
}
```

#### 缓存优化
- 启用向量缓存减少重复计算
- 使用Redis缓存频繁查询结果
- 实现智能预加载机制

---

## 🧪 测试与验证

### 🔬 功能测试

运行完整的功能测试：

```bash
# 运行配置测试
python test_deepseek_config.py

# 测试API端点
curl http://localhost:8000/health

# 测试记忆添加
python -c "
from mem0 import Memory
config = {...}  # 您的配置
memory = Memory.from_config(config)
result = memory.add([{'role': 'user', 'content': '测试记忆'}], user_id='test')
print('测试成功:', result)
"
```

### 📊 性能测试

```bash
# 使用Apache Bench进行压力测试
ab -n 100 -c 10 http://localhost:8000/health

# 使用curl测试响应时间
time curl -X POST "http://localhost:8000/v1/memories" \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "测试"}], "user_id": "test"}'
```

### 🐛 调试模式

启用调试模式获取详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 或在启动时设置环境变量
export LOG_LEVEL=DEBUG
python deepseek_server.py
```

---

## 🤝 贡献指南

### 🌟 如何贡献

我们欢迎社区贡献！请遵循以下步骤：

1. **Fork 项目仓库**
2. **创建功能分支** (`git checkout -b feature/AmazingFeature`)
3. **提交更改** (`git commit -m 'Add some AmazingFeature'`)
4. **推送到分支** (`git push origin feature/AmazingFeature`)
5. **创建 Pull Request**

### 📝 贡献类型

- 🐛 **Bug修复**: 报告和修复系统问题
- ✨ **新功能**: 添加新的功能特性
- 📚 **文档改进**: 完善文档和示例
- 🎨 **UI/UX改进**: 改善用户界面和体验
- ⚡ **性能优化**: 提升系统性能
- 🧪 **测试用例**: 添加测试覆盖

### 🔍 代码规范

```python
# 代码风格示例
def add_memory(messages: List[Dict], user_id: str) -> Dict:
    """
    添加记忆到系统
    
    Args:
        messages: 对话消息列表
        user_id: 用户ID
        
    Returns:
        Dict: 添加结果
    """
    try:
        # 实现逻辑
        return {"status": "success"}
    except Exception as e:
        logger.error(f"添加记忆失败: {e}")
        raise
```

---

## 📞 支持与社区

### 🆘 获取帮助

- **📖 文档**: 查看本README和API文档
- **🐛 问题反馈**: [GitHub Issues](https://github.com/mem0ai/mem0/issues)
- **💡 功能建议**: 欢迎提交功能请求
- **💬 技术交流**: 加入我们的技术讨论

### 🌐 社区资源

- **官方网站**: [mem0.ai](https://mem0.ai)
- **Discord社区**: [加入讨论](https://discord.gg/6PzXDgEjG5)
- **GitHub仓库**: [mem0ai/mem0](https://github.com/mem0ai/mem0)
- **技术博客**: 关注最新技术动态

### 📧 联系方式

- **技术支持**: 通过GitHub Issues提交
- **商务合作**: 通过官方网站联系
- **安全问题**: 请私下报告安全漏洞

---

## 📄 许可证

本项目基于 **Apache 2.0** 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情。

### 许可证要点
- ✅ 商业使用
- ✅ 修改代码
- ✅ 分发代码
- ✅ 私人使用
- ❗ 需要包含许可证和版权声明
- ❗ 需要说明重大修改

---

## 🎯 路线图

### 🔮 未来计划

#### v1.1 (计划中)
- [ ] Web管理界面
- [ ] 批量导入/导出功能
- [ ] 更多嵌入模型支持
- [ ] 性能监控面板

#### v1.2 (规划中)
- [ ] 多模态记忆支持（图像、音频）
- [ ] 分布式部署支持
- [ ] 更多LLM提供商集成
- [ ] 高级搜索功能

#### v2.0 (长期目标)
- [ ] 图数据库集成
- [ ] 联邦学习支持
- [ ] 企业级功能
- [ ] 云原生部署

---

## 🏆 致谢

### 🙏 特别感谢

- **Mem0团队**: 提供优秀的基础架构
- **DeepSeek**: 提供高性能的LLM服务
- **Ollama社区**: 提供本地AI模型运行环境
- **ChromaDB**: 提供高效的向量数据库
- **开源社区**: 所有贡献者和用户的支持

### 🌟 技术栈

- **后端框架**: FastAPI + Uvicorn
- **AI模型**: DeepSeek + BGE-M3
- **数据库**: ChromaDB
- **开发语言**: Python 3.8+
- **容器化**: Docker (可选)

---

<p align="center">
  <strong>🎉 开始使用您的智能记忆助手，让AI记住每一个重要时刻！</strong>
</p>

<p align="center">
  <strong>🧠✨ Mem0-DeepSeek - 您的专属AI记忆管家 ✨🧠</strong>
</p>

---

<p align="center">
  <sub>如果这个项目对您有帮助，请给我们一个 ⭐ Star！</sub>
</p>
