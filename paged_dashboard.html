<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Mem0-DeepSeek 分页式智能记忆仪表盘</title>
    
    <!-- 引入ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入Prism.js用于代码高亮 -->
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            min-height: calc(100vh - 40px);
        }

        /* Header样式 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.8em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.3em;
            opacity: 0.95;
        }

        /* 统计面板样式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            padding: 35px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .stat-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.8);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
        }

        .stat-card:hover {
            transform: translateY(-6px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.12);
        }

        .stat-card .icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .stat-card .value {
            font-size: 2.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .stat-card .label {
            color: #666;
            font-size: 1.1em;
            font-weight: 500;
        }

        /* 导航标签样式 */
        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
            overflow-x: auto;
        }

        .nav-tab {
            flex: 1;
            min-width: 200px;
            padding: 20px 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 4px solid transparent;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            position: relative;
            overflow: hidden;
        }

        .nav-tab:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            transform: translateY(-2px);
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-bottom-color: #0056b3;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        }

        .nav-tab .tab-icon {
            font-size: 2em;
            display: block;
            margin-bottom: 8px;
        }

        .nav-tab .tab-title {
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .nav-tab .tab-desc {
            font-size: 0.85em;
            opacity: 0.8;
        }

        .nav-tab.active .tab-desc {
            opacity: 0.9;
        }

        /* 内容区域 */
        .content-area {
            padding: 40px;
            min-height: 600px;
            background: #f8f9fa;
        }

        .page-content {
            display: none;
            animation: fadeIn 0.3s ease-in-out;
        }

        .page-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 大卡片样式 */
        .main-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 12px 40px rgba(0,0,0,0.1);
            border: 1px solid #e1e5e9;
            margin-bottom: 30px;
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .card-header .icon {
            font-size: 2.5em;
            color: #4facfe;
        }

        .card-header .title {
            font-size: 2em;
            font-weight: bold;
            color: #333;
        }

        .card-header .description {
            color: #666;
            font-size: 1.1em;
            margin-top: 5px;
        }

        /* 表单样式 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: #333;
            font-size: 1em;
        }

        .form-group input, 
        .form-group textarea, 
        .form-group select {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-group input:focus, 
        .form-group textarea:focus, 
        .form-group select:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 4px rgba(79, 172, 254, 0.1);
        }

        .form-group textarea {
            height: 120px;
            resize: vertical;
        }

        /* 按钮样式 */
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 30px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 15px;
            margin-bottom: 15px;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }

        .btn-secondary:hover {
            box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
        }

        /* 图表容器 */
        .chart-container {
            width: 100%;
            height: 450px;
            margin-top: 20px;
            border-radius: 15px;
            overflow: hidden;
            background: white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin-top: 20px;
        }

        .chart-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid #e1e5e9;
        }

        .chart-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chart-title .icon {
            font-size: 1.5em;
            color: #4facfe;
        }

        /* 加载动画 */
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .loading .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 结果展示 */
        .results {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-top: 25px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
        }

        .memory-item {
            background: white;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 12px;
            border-left: 5px solid #4facfe;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }

        .memory-item .memory-text {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.5;
            font-size: 1.05em;
        }

        .memory-item .memory-meta {
            color: #666;
            font-size: 0.9em;
        }

        /* 内容类型标签 */
        .content-type-tag {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            margin-right: 10px;
        }

        .type-short_text { background: #e3f2fd; color: #1976d2; }
        .type-long_text { background: #f3e5f5; color: #7b1fa2; }
        .type-code { background: #e8f5e8; color: #388e3c; }
        .type-url { background: #fff3e0; color: #f57c00; }
        .type-json { background: #fce4ec; color: #c2185b; }
        .type-image { background: #e1f5fe; color: #0277bd; }
        .type-audio { background: #f3e5f5; color: #7b1fa2; }
        .type-video { background: #e8f5e8; color: #2e7d32; }
        .type-document { background: #fff8e1; color: #f57c00; }
        .type-pdf { background: #ffebee; color: #c62828; }

        /* 异步处理进度条样式 */
        .async-progress-container {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border: 1px solid #e9ecef;
        }

        .async-progress-bar {
            width: 100%;
            height: 24px;
            background: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 15px;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .async-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 12px;
            transition: width 0.5s ease;
            position: relative;
            overflow: hidden;
        }

        .async-progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .async-progress-text {
            text-align: center;
            color: #495057;
            font-weight: 600;
            font-size: 1em;
        }

        .async-progress-stages {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            font-size: 0.9em;
        }

        .async-stage {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #6c757d;
            transition: color 0.3s ease;
        }

        .async-stage.active {
            color: #4facfe;
            font-weight: bold;
        }

        .async-stage.completed {
            color: #28a745;
        }

        .async-stage-icon {
            font-size: 1.2em;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .nav-tabs {
                flex-direction: column;
            }
            
            .nav-tab {
                min-width: auto;
            }
            
            .content-area {
                padding: 20px;
            }
            
            .main-card {
                padding: 25px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 25px;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2.2em;
            }
            
            .header p {
                font-size: 1.1em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🧠 Mem0-DeepSeek 分页式智能记忆仪表盘</h1>
            <p>基于DeepSeek + Ollama的高性能本地记忆管理平台 - 分页式设计</p>
        </div>

        <!-- 统计面板 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="icon">🎯</div>
                <div class="value" id="cacheHitRate">-</div>
                <div class="label">缓存命中率</div>
            </div>
            <div class="stat-card">
                <div class="icon">📊</div>
                <div class="value" id="totalRequests">-</div>
                <div class="label">总API调用</div>
            </div>
            <div class="stat-card">
                <div class="icon">💾</div>
                <div class="value" id="cachedItems">-</div>
                <div class="label">缓存项目</div>
            </div>
            <div class="stat-card">
                <div class="icon">⚡</div>
                <div class="value" id="avgResponseTime">-</div>
                <div class="label">平均响应时间</div>
            </div>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs">
            <div class="nav-tab active" onclick="switchPage('operations')">
                <span class="tab-icon">🛠️</span>
                <div class="tab-title">记忆操作</div>
                <div class="tab-desc">添加、搜索、管理记忆</div>
            </div>
            <div class="nav-tab" onclick="switchPage('visualization')">
                <span class="tab-icon">📊</span>
                <div class="tab-title">数据可视化</div>
                <div class="tab-desc">图表分析和统计</div>
            </div>
            <div class="nav-tab" onclick="switchPage('performance')">
                <span class="tab-icon">⚡</span>
                <div class="tab-title">性能监控</div>
                <div class="tab-desc">系统性能和监控</div>
            </div>
            <div class="nav-tab" onclick="switchPage('settings')">
                <span class="tab-icon">⚙️</span>
                <div class="tab-title">系统设置</div>
                <div class="tab-desc">配置和管理工具</div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 记忆操作页面 -->
            <div id="operations-page" class="page-content active">
                <!-- 添加记忆卡片 -->
                <div class="main-card">
                    <div class="card-header">
                        <span class="icon">📝</span>
                        <div>
                            <div class="title">添加记忆</div>
                            <div class="description">输入文本内容，系统将自动提取关键信息并生成记忆片段</div>
                        </div>
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="userMessage">记忆内容:</label>
                            <textarea id="userMessage" placeholder="输入要记住的内容，例如：我是张三，今年30岁，是一名软件工程师..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="userId">用户ID:</label>
                            <input type="text" id="userId" placeholder="输入用户ID" value="dashboard_user_001">
                        </div>
                    </div>
                    
                    <button class="btn" onclick="addMemory()">
                        <span>💾</span> 添加记忆
                    </button>
                    <button class="btn btn-secondary" onclick="clearAddForm()">
                        <span>🗑️</span> 清空表单
                    </button>
                    
                    <div class="loading" id="addLoading">
                        <div class="spinner"></div>
                        <p>正在添加记忆...</p>
                    </div>
                    
                    <div class="results" id="addResults" style="display: none;">
                        <div id="addResultsContent"></div>
                    </div>
                </div>

                <!-- 文件上传卡片 -->
                <div class="main-card">
                    <div class="card-header">
                        <span class="icon">📁</span>
                        <div>
                            <div class="title">文件上传</div>
                            <div class="description">上传各种格式的文件，支持代码、图片、文档等多种类型</div>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="uploadFile">选择文件:</label>
                            <input type="file" id="uploadFile" accept="*/*" style="padding: 12px;">
                        </div>
                        <div class="form-group">
                            <label for="uploadDescription">文件描述 (可选):</label>
                            <textarea id="uploadDescription" placeholder="描述这个文件的内容或用途..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="uploadUserId">用户ID:</label>
                            <input type="text" id="uploadUserId" placeholder="输入用户ID" value="dashboard_user_001">
                        </div>
                    </div>

                    <button class="btn" onclick="uploadFile()">
                        <span>📁</span> 上传文件
                    </button>
                    <button class="btn btn-secondary" onclick="clearUploadForm()">
                        <span>🗑️</span> 清空表单
                    </button>

                    <div class="loading" id="uploadLoading">
                        <div class="spinner"></div>
                        <p>正在上传文件...</p>
                    </div>

                    <div class="results" id="uploadResults" style="display: none;">
                        <div id="uploadResultsContent"></div>
                    </div>
                </div>

                <!-- 搜索记忆卡片 -->
                <div class="main-card">
                    <div class="card-header">
                        <span class="icon">🔍</span>
                        <div>
                            <div class="title">搜索记忆</div>
                            <div class="description">使用智能语义搜索，快速找到相关的记忆内容</div>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="searchQuery">搜索查询:</label>
                            <input type="text" id="searchQuery" placeholder="输入搜索关键词，例如：张三的职业是什么？">
                        </div>
                        <div class="form-group">
                            <label for="searchUserId">用户ID:</label>
                            <input type="text" id="searchUserId" placeholder="输入用户ID" value="dashboard_user_001">
                        </div>
                        <div class="form-group">
                            <label for="searchLimit">结果数量:</label>
                            <input type="number" id="searchLimit" value="5" min="1" max="20">
                        </div>
                    </div>

                    <button class="btn" onclick="searchMemories()">
                        <span>🔍</span> 搜索记忆
                    </button>
                    <button class="btn btn-secondary" onclick="clearSearchForm()">
                        <span>🗑️</span> 清空表单
                    </button>

                    <div class="loading" id="searchLoading">
                        <div class="spinner"></div>
                        <p>正在搜索记忆...</p>
                    </div>

                    <div class="results" id="searchResults" style="display: none;">
                        <div id="searchResultsContent"></div>
                    </div>
                </div>
            </div>

            <!-- 数据可视化页面 -->
            <div id="visualization-page" class="page-content">
                <div class="main-card">
                    <div class="card-header">
                        <span class="icon">📊</span>
                        <div>
                            <div class="title">数据可视化分析</div>
                            <div class="description">通过图表直观展示记忆数据的分布和趋势</div>
                        </div>
                    </div>

                    <div class="charts-grid">
                        <div class="chart-card">
                            <div class="chart-title">
                                <span class="icon">📊</span>
                                记忆类型分布
                            </div>
                            <div id="memoryTypesChart" class="chart-container"></div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-title">
                                <span class="icon">👥</span>
                                用户活跃度统计
                            </div>
                            <div id="userActivityChart" class="chart-container"></div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-title">
                                <span class="icon">🏷️</span>
                                热门关键词云
                            </div>
                            <div id="keywordsChart" class="chart-container"></div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-title">
                                <span class="icon">🕸️</span>
                                记忆关联网络
                            </div>
                            <div id="networkChart" class="chart-container"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 性能监控页面 -->
            <div id="performance-page" class="page-content">
                <div class="main-card">
                    <div class="card-header">
                        <span class="icon">⚡</span>
                        <div>
                            <div class="title">系统性能监控</div>
                            <div class="description">实时监控系统性能指标和运行状态</div>
                        </div>
                    </div>

                    <div class="charts-grid">
                        <div class="chart-card">
                            <div class="chart-title">
                                <span class="icon">⚡</span>
                                API响应时间趋势
                            </div>
                            <div id="responseTimeChart" class="chart-container"></div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-title">
                                <span class="icon">📈</span>
                                请求频率统计
                            </div>
                            <div id="requestFrequencyChart" class="chart-container"></div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-title">
                                <span class="icon">🚨</span>
                                系统健康监控
                            </div>
                            <div id="errorRateChart" class="chart-container"></div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-title">
                                <span class="icon">💾</span>
                                缓存性能分析
                            </div>
                            <div id="cacheAnalysisChart" class="chart-container"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统设置页面 -->
            <div id="settings-page" class="page-content">
                <div class="main-card">
                    <div class="card-header">
                        <span class="icon">⚙️</span>
                        <div>
                            <div class="title">系统管理工具</div>
                            <div class="description">系统配置、数据管理和维护工具</div>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="manageUserId">用户ID:</label>
                            <input type="text" id="manageUserId" placeholder="输入用户ID" value="dashboard_user_001">
                        </div>
                    </div>

                    <button class="btn" onclick="getAllMemories()">
                        <span>📋</span> 获取所有记忆
                    </button>
                    <button class="btn" onclick="exportData()">
                        <span>📤</span> 导出数据
                    </button>
                    <button class="btn" onclick="refreshAllCharts()">
                        <span>📊</span> 刷新图表
                    </button>
                    <button class="btn btn-secondary" onclick="checkSystemHealth()">
                        <span>❤️</span> 系统检查
                    </button>

                    <div class="loading" id="manageLoading">
                        <div class="spinner"></div>
                        <p>正在处理请求...</p>
                    </div>

                    <div class="results" id="manageResults" style="display: none;">
                        <div id="manageResultsContent"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        // 图表实例存储
        const charts = {};

        // 当前活跃页面
        let currentPage = 'operations';

        // 异步处理相关变量
        let websocket = null;
        let connectionId = null;
        let asyncEnabled = false;
        let currentTaskId = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshStats();
            initializeAsyncSupport();
        });

        // 初始化异步支持
        async function initializeAsyncSupport() {
            try {
                // 检查系统是否支持异步处理
                const response = await fetch(`${API_BASE}/v1/system/status`);
                const systemStatus = await response.json();

                asyncEnabled = systemStatus.async_mode === 'available';

                if (asyncEnabled) {
                    console.log('✅ 异步处理可用，初始化WebSocket连接');
                    initializeWebSocket();
                } else {
                    console.log('⚠️ 异步处理不可用，使用同步模式');
                }
            } catch (error) {
                console.error('❌ 检查异步支持失败，使用同步模式:', error);
                asyncEnabled = false;
            }
        }

        // 初始化WebSocket连接
        function initializeWebSocket() {
            try {
                connectionId = 'dashboard_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                const wsUrl = `ws://localhost:8000/ws/${connectionId}`;

                websocket = new WebSocket(wsUrl);

                websocket.onopen = function(event) {
                    console.log('✅ WebSocket连接成功');
                };

                websocket.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        handleWebSocketMessage(message);
                    } catch (error) {
                        console.error('❌ 处理WebSocket消息失败:', error);
                    }
                };

                websocket.onclose = function(event) {
                    console.log('🔌 WebSocket连接已关闭');
                    // 5秒后尝试重连
                    setTimeout(() => {
                        if (asyncEnabled) {
                            console.log('🔄 尝试重连WebSocket');
                            initializeWebSocket();
                        }
                    }, 5000);
                };

                websocket.onerror = function(error) {
                    console.error('❌ WebSocket连接错误:', error);
                    asyncEnabled = false; // 禁用异步处理
                };

            } catch (error) {
                console.error('❌ 初始化WebSocket失败:', error);
                asyncEnabled = false;
            }
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(message) {
            try {
                switch (message.type) {
                    case 'connection_established':
                        console.log('✅ WebSocket连接确认');
                        break;

                    case 'task_progress':
                        updateTaskProgress(message.task_id, message.progress, message.stage);
                        break;

                    case 'task_completed':
                        handleTaskCompleted(message.task_id, message.result);
                        break;

                    case 'task_failed':
                        handleTaskFailed(message.task_id, message.error);
                        break;

                    case 'pong':
                        // 心跳响应
                        break;

                    default:
                        console.log('📨 收到未知消息类型:', message.type);
                }
            } catch (error) {
                console.error('❌ 处理WebSocket消息失败:', error);
            }
        }

        // 切换页面
        function switchPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });
            
            // 移除所有标签的active状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中页面
            document.getElementById(pageId + '-page').classList.add('active');
            
            // 激活选中标签
            event.target.closest('.nav-tab').classList.add('active');
            
            // 更新当前页面
            currentPage = pageId;
            
            // 如果是可视化或性能页面，初始化图表
            if (pageId === 'visualization' || pageId === 'performance') {
                setTimeout(() => {
                    initializePageCharts(pageId);
                }, 100);
            }
        }

        // 刷新统计信息
        async function refreshStats() {
            try {
                const [performanceResponse, analyticsResponse] = await Promise.all([
                    fetch(`${API_BASE}/performance`),
                    fetch(`${API_BASE}/v1/analytics/performance`)
                ]);
                
                const performanceData = await performanceResponse.json();
                const analyticsData = await analyticsResponse.json();
                
                // 更新统计卡片
                const cache = performanceData.cache_performance;
                document.getElementById('cacheHitRate').textContent = 
                    (cache.hit_rate * 100).toFixed(1) + '%';
                document.getElementById('totalRequests').textContent = 
                    analyticsData.data.total_api_calls || 0;
                document.getElementById('cachedItems').textContent = cache.cached_items;
                
                // 计算平均响应时间
                const responseTimes = analyticsData.data.response_times || [];
                const avgTime = responseTimes.length > 0 
                    ? responseTimes.reduce((sum, item) => sum + item.duration, 0) / responseTimes.length
                    : 0;
                document.getElementById('avgResponseTime').textContent = avgTime.toFixed(2) + 's';
                
            } catch (error) {
                console.error('获取统计信息失败:', error);
            }
        }

        // 初始化页面图表
        function initializePageCharts(pageId) {
            if (pageId === 'visualization') {
                initializeVisualizationCharts();
            } else if (pageId === 'performance') {
                initializePerformanceCharts();
            }
        }

        // 初始化可视化图表
        function initializeVisualizationCharts() {
            const chartIds = ['memoryTypesChart', 'userActivityChart', 'keywordsChart', 'networkChart'];
            chartIds.forEach(chartId => {
                const container = document.getElementById(chartId);
                if (container && !charts[chartId]) {
                    charts[chartId] = echarts.init(container);
                    updateChart(chartId);
                }
            });
        }

        // 初始化性能图表
        function initializePerformanceCharts() {
            const chartIds = ['responseTimeChart', 'requestFrequencyChart', 'errorRateChart', 'cacheAnalysisChart'];
            chartIds.forEach(chartId => {
                const container = document.getElementById(chartId);
                if (container && !charts[chartId]) {
                    charts[chartId] = echarts.init(container);
                    updateChart(chartId);
                }
            });
        }

        // 更新特定图表
        async function updateChart(chartId) {
            switch(chartId) {
                case 'memoryTypesChart':
                    await updateMemoryTypesChart();
                    break;
                case 'userActivityChart':
                    await updateUserActivityChart();
                    break;
                case 'keywordsChart':
                    await updateKeywordsChart();
                    break;
                case 'networkChart':
                    await updateNetworkChart();
                    break;
                case 'responseTimeChart':
                    await updateResponseTimeChart();
                    break;
                case 'requestFrequencyChart':
                    await updateRequestFrequencyChart();
                    break;
                case 'errorRateChart':
                    await updateErrorRateChart();
                    break;
                case 'cacheAnalysisChart':
                    await updateCacheAnalysisChart();
                    break;
            }
        }

        // 添加记忆功能 - 支持异步处理的安全版本
        async function addMemory() {
            const userMessage = document.getElementById('userMessage').value.trim();
            const userId = document.getElementById('userId').value.trim();

            if (!userMessage || !userId) {
                alert('请填写记忆内容和用户ID');
                return;
            }

            const loading = document.getElementById('addLoading');
            const results = document.getElementById('addResults');
            const resultsContent = document.getElementById('addResultsContent');

            loading.style.display = 'block';
            results.style.display = 'none';

            // 尝试异步处理
            if (asyncEnabled && websocket && websocket.readyState === WebSocket.OPEN) {
                try {
                    await addMemoryAsync(userMessage, userId, loading, results, resultsContent);
                    return;
                } catch (error) {
                    console.error('❌ 异步处理失败，回退到同步处理:', error);
                    // 继续执行同步处理
                }
            }

            // 同步处理（原有逻辑）
            try {
                const startTime = Date.now();
                const response = await fetch(`${API_BASE}/v1/memories`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        messages: [
                            { role: 'user', content: userMessage },
                            { role: 'assistant', content: '我已经记住了这个信息。' }
                        ],
                        user_id: userId
                    })
                });

                const data = await response.json();
                const endTime = Date.now();
                const duration = ((endTime - startTime) / 1000).toFixed(2);

                let html = `<h3>✅ 添加成功 (${duration}秒)</h3>`;

                if (data.content_analysis) {
                    const analysis = data.content_analysis;
                    html += `<div class="memory-item">`;
                    html += `<div class="memory-text">内容分析</div>`;
                    html += `<div class="memory-meta">`;
                    html += `<span class="content-type-tag type-${analysis.type}">${analysis.type}</span>`;
                    html += `记忆片段: ${analysis.memory_count} 个 | `;
                    html += `关键词: ${analysis.keywords.slice(0, 3).join(', ')}`;
                    html += `</div></div>`;
                }

                if (data.results) {
                    html += '<h4>提取的记忆片段:</h4>';
                    data.results.forEach((item, index) => {
                        html += `<div class="memory-item">`;
                        html += `<div class="memory-text">${index + 1}. ${item.memory}</div>`;
                        html += `<div class="memory-meta">ID: ${item.id}</div>`;
                        html += `</div>`;
                    });
                }

                resultsContent.innerHTML = html;
                results.style.display = 'block';

                // 刷新统计
                refreshStats();
            } catch (error) {
                resultsContent.innerHTML = `<p>❌ 添加失败: ${error.message}</p>`;
                results.style.display = 'block';
            } finally {
                loading.style.display = 'none';
            }
        }

        // 异步添加记忆
        async function addMemoryAsync(userMessage, userId, loading, results, resultsContent) {
            try {
                console.log('🚀 使用异步处理模式');

                // 调用异步API
                const response = await fetch(`${API_BASE}/v1/memories/async`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        messages: [
                            { role: 'user', content: userMessage },
                            { role: 'assistant', content: '我已经记住了这个信息。' }
                        ],
                        user_id: userId
                    })
                });

                const data = await response.json();

                if (data.task_id) {
                    currentTaskId = data.task_id;

                    // 订阅任务进度
                    websocket.send(JSON.stringify({
                        type: 'subscribe_task',
                        task_id: currentTaskId
                    }));

                    // 显示异步处理状态
                    resultsContent.innerHTML = `
                        <h3>🚀 异步处理中...</h3>
                        <div class="memory-item">
                            <div class="memory-text">任务ID: ${currentTaskId}</div>
                            <div class="memory-meta">预计处理时间: ${data.estimated_time}</div>
                        </div>
                        <div class="async-progress-container">
                            <div class="async-progress-bar">
                                <div class="async-progress-fill" id="asyncProgressFill" style="width: 0%;"></div>
                            </div>
                            <div class="async-progress-text" id="asyncProgressText">准备处理...</div>
                            <div class="async-progress-stages">
                                <div class="async-stage" id="stage1">
                                    <span class="async-stage-icon">📝</span>
                                    <span>内容分析</span>
                                </div>
                                <div class="async-stage" id="stage2">
                                    <span class="async-stage-icon">🧠</span>
                                    <span>LLM处理</span>
                                </div>
                                <div class="async-stage" id="stage3">
                                    <span class="async-stage-icon">🔗</span>
                                    <span>向量生成</span>
                                </div>
                                <div class="async-stage" id="stage4">
                                    <span class="async-stage-icon">💾</span>
                                    <span>存储完成</span>
                                </div>
                            </div>
                        </div>
                    `;
                    results.style.display = 'block';
                    loading.style.display = 'none';

                } else {
                    throw new Error('异步任务创建失败');
                }

            } catch (error) {
                console.error('❌ 异步处理失败:', error);
                throw error;
            }
        }

        // 文件上传功能
        async function uploadFile() {
            const fileInput = document.getElementById('uploadFile');
            const description = document.getElementById('uploadDescription').value.trim();
            const userId = document.getElementById('uploadUserId').value.trim();

            if (!fileInput.files[0] || !userId) {
                alert('请选择文件并输入用户ID');
                return;
            }

            const file = fileInput.files[0];
            const loading = document.getElementById('uploadLoading');
            const results = document.getElementById('uploadResults');
            const resultsContent = document.getElementById('uploadResultsContent');

            loading.style.display = 'block';
            results.style.display = 'none';

            try {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('user_id', userId);
                formData.append('description', description);

                const startTime = Date.now();
                const response = await fetch(`${API_BASE}/v1/memories/upload`, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                const endTime = Date.now();
                const duration = ((endTime - startTime) / 1000).toFixed(2);

                let html = `<h3>✅ 文件上传成功 (${duration}秒)</h3>`;

                if (data.file_info) {
                    const fileInfo = data.file_info;
                    html += `<div class="memory-item">`;
                    html += `<div class="memory-text">文件信息</div>`;
                    html += `<div class="memory-meta">`;
                    html += `<span class="content-type-tag type-${fileInfo.content_type}">${fileInfo.content_type}</span>`;
                    html += `文件名: ${fileInfo.filename} | `;
                    html += `大小: ${(fileInfo.file_size / 1024).toFixed(2)} KB | `;
                    html += `类型: ${fileInfo.mime_type}`;
                    html += `</div></div>`;
                }

                if (data.content_analysis) {
                    const analysis = data.content_analysis;
                    html += `<div class="memory-item">`;
                    html += `<div class="memory-text">内容分析</div>`;
                    html += `<div class="memory-meta">`;
                    html += `记忆片段: ${analysis.memory_count} 个 | `;
                    html += `关键词: ${analysis.keywords.slice(0, 3).join(', ')}`;
                    html += `</div></div>`;
                }

                if (data.results) {
                    html += '<h4>提取的记忆片段:</h4>';
                    data.results.forEach((item, index) => {
                        html += `<div class="memory-item">`;
                        html += `<div class="memory-text">${index + 1}. ${item.memory}</div>`;
                        html += `<div class="memory-meta">ID: ${item.id}</div>`;
                        html += `</div>`;
                    });
                }

                resultsContent.innerHTML = html;
                results.style.display = 'block';

                // 刷新统计
                refreshStats();
            } catch (error) {
                resultsContent.innerHTML = `<p>❌ 上传失败: ${error.message}</p>`;
                results.style.display = 'block';
            } finally {
                loading.style.display = 'none';
            }
        }

        // 搜索记忆功能
        async function searchMemories() {
            const query = document.getElementById('searchQuery').value.trim();
            const userId = document.getElementById('searchUserId').value.trim();
            const limit = parseInt(document.getElementById('searchLimit').value);

            if (!query || !userId) {
                alert('请填写搜索查询和用户ID');
                return;
            }

            const loading = document.getElementById('searchLoading');
            const results = document.getElementById('searchResults');
            const resultsContent = document.getElementById('searchResultsContent');

            loading.style.display = 'block';
            results.style.display = 'none';

            try {
                const startTime = Date.now();
                const response = await fetch(`${API_BASE}/v1/memories/search`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        user_id: userId,
                        limit: limit
                    })
                });

                const data = await response.json();
                const endTime = Date.now();
                const duration = ((endTime - startTime) / 1000).toFixed(2);

                let html = `<h3>🔍 搜索结果 (${duration}秒)</h3>`;

                if (data.results && data.results.length > 0) {
                    data.results.forEach((memory, index) => {
                        html += `<div class="memory-item">`;
                        html += `<div class="memory-text">${memory.memory}</div>`;
                        html += `<div class="memory-meta">`;
                        html += `ID: ${memory.id} | `;
                        html += `相关度: ${(memory.score * 100).toFixed(1)}% | `;
                        html += `创建时间: ${memory.created_at || '未知'}`;
                        html += `</div></div>`;
                    });
                } else {
                    html += '<p>未找到相关记忆</p>';
                }

                resultsContent.innerHTML = html;
                results.style.display = 'block';
            } catch (error) {
                resultsContent.innerHTML = `<p>❌ 搜索失败: ${error.message}</p>`;
                results.style.display = 'block';
            } finally {
                loading.style.display = 'none';
            }
        }

        // 获取所有记忆
        async function getAllMemories() {
            const userId = document.getElementById('manageUserId').value.trim();

            if (!userId) {
                alert('请输入用户ID');
                return;
            }

            const loading = document.getElementById('manageLoading');
            const results = document.getElementById('manageResults');
            const resultsContent = document.getElementById('manageResultsContent');

            loading.style.display = 'block';
            results.style.display = 'none';

            try {
                const response = await fetch(`${API_BASE}/v1/memories?user_id=${userId}&limit=20`);
                const data = await response.json();

                let html = `<h3>📋 用户记忆列表</h3>`;

                if (data.results && data.results.length > 0) {
                    data.results.forEach((memory, index) => {
                        html += `<div class="memory-item">`;
                        html += `<div class="memory-text">${memory.memory}</div>`;
                        html += `<div class="memory-meta">`;
                        html += `ID: ${memory.id} | `;
                        html += `创建时间: ${memory.created_at || '未知'}`;
                        html += `</div></div>`;
                    });
                } else {
                    html += '<p>该用户暂无记忆</p>';
                }

                resultsContent.innerHTML = html;
                results.style.display = 'block';
            } catch (error) {
                resultsContent.innerHTML = `<p>❌ 获取失败: ${error.message}</p>`;
                results.style.display = 'block';
            } finally {
                loading.style.display = 'none';
            }
        }

        // 导出数据
        async function exportData() {
            try {
                const response = await fetch(`${API_BASE}/v1/memories?limit=1000`);
                const data = await response.json();

                const exportData = {
                    export_time: new Date().toISOString(),
                    total_memories: data.results?.length || 0,
                    memories: data.results || []
                };

                const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `mem0_export_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                alert(`✅ 导出完成！已导出 ${exportData.total_memories} 条记忆数据`);
            } catch (error) {
                alert(`❌ 导出失败: ${error.message}`);
            }
        }

        // 系统健康检查
        async function checkSystemHealth() {
            const loading = document.getElementById('manageLoading');
            const results = document.getElementById('manageResults');
            const resultsContent = document.getElementById('manageResultsContent');

            loading.style.display = 'block';
            results.style.display = 'none';

            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();

                let html = `<h3>❤️ 系统健康检查</h3>`;
                html += `<div class="memory-item">`;
                html += `<div class="memory-text">系统状态: ${data.status}</div>`;
                html += `<div class="memory-meta">检查时间: ${data.timestamp || new Date().toISOString()}</div>`;
                html += `</div>`;

                if (data.components) {
                    html += `<h4>组件状态:</h4>`;
                    Object.entries(data.components).forEach(([component, status]) => {
                        html += `<div class="memory-item">`;
                        html += `<div class="memory-text">${component}: ${status}</div>`;
                        html += `</div>`;
                    });
                }

                resultsContent.innerHTML = html;
                results.style.display = 'block';
            } catch (error) {
                resultsContent.innerHTML = `<p>❌ 健康检查失败: ${error.message}</p>`;
                results.style.display = 'block';
            } finally {
                loading.style.display = 'none';
            }
        }

        // 刷新所有图表
        function refreshAllCharts() {
            for (const chartId in charts) {
                updateChart(chartId);
            }
        }

        // 清空表单函数
        function clearAddForm() {
            document.getElementById('userMessage').value = '';
            document.getElementById('addResults').style.display = 'none';
        }

        function clearUploadForm() {
            document.getElementById('uploadFile').value = '';
            document.getElementById('uploadDescription').value = '';
            document.getElementById('uploadResults').style.display = 'none';
        }

        function clearSearchForm() {
            document.getElementById('searchQuery').value = '';
            document.getElementById('searchResults').style.display = 'none';
        }

        // 图表更新函数
        async function updateMemoryTypesChart() {
            try {
                const response = await fetch(`${API_BASE}/v1/analytics/memory-types`);
                const data = await response.json();

                const option = {
                    title: {
                        text: '记忆类型分布',
                        left: 'center',
                        textStyle: { fontSize: 18, fontWeight: 'bold' }
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b}: {c} ({d}%)'
                    },
                    legend: {
                        orient: 'vertical',
                        left: 'left',
                        data: data.data.map(item => item.type)
                    },
                    series: [{
                        name: '记忆类型',
                        type: 'pie',
                        radius: '65%',
                        data: data.data.map(item => ({
                            value: item.count,
                            name: item.type
                        })),
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }]
                };

                charts.memoryTypesChart.setOption(option);
            } catch (error) {
                console.error('更新记忆类型图表失败:', error);
            }
        }

        async function updateUserActivityChart() {
            try {
                const response = await fetch(`${API_BASE}/v1/analytics/performance`);
                const data = await response.json();

                const activities = data.data.user_activities || [];

                const option = {
                    title: {
                        text: '用户活跃度统计',
                        left: 'center',
                        textStyle: { fontSize: 18, fontWeight: 'bold' }
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: { type: 'shadow' }
                    },
                    xAxis: {
                        type: 'category',
                        data: activities.map(item => item.user_id.substring(0, 15) + '...')
                    },
                    yAxis: {
                        type: 'value',
                        name: '请求数量'
                    },
                    series: [{
                        name: '总请求数',
                        type: 'bar',
                        data: activities.map(item => item.total_requests),
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: '#52c41a'},
                                {offset: 1, color: '#389e0d'}
                            ])
                        }
                    }]
                };

                charts.userActivityChart.setOption(option);
            } catch (error) {
                console.error('更新用户活跃度图表失败:', error);
            }
        }

        async function updateKeywordsChart() {
            const keywords = [
                {name: '程序员', value: 15},
                {name: 'Python', value: 12},
                {name: '设计师', value: 10},
                {name: '产品经理', value: 8},
                {name: 'JavaScript', value: 7},
                {name: '数据分析', value: 6},
                {name: '机器学习', value: 5}
            ];

            const option = {
                title: {
                    text: '热门关键词',
                    left: 'center',
                    textStyle: { fontSize: 18, fontWeight: 'bold' }
                },
                tooltip: {
                    trigger: 'item'
                },
                series: [{
                    type: 'wordCloud',
                    gridSize: 2,
                    sizeRange: [12, 60],
                    rotationRange: [-90, 90],
                    shape: 'pentagon',
                    width: '100%',
                    height: '100%',
                    drawOutOfBound: true,
                    textStyle: {
                        fontFamily: 'sans-serif',
                        fontWeight: 'bold',
                        color: function () {
                            return 'rgb(' + [
                                Math.round(Math.random() * 160),
                                Math.round(Math.random() * 160),
                                Math.round(Math.random() * 160)
                            ].join(',') + ')';
                        }
                    },
                    emphasis: {
                        textStyle: {
                            shadowBlur: 10,
                            shadowColor: '#333'
                        }
                    },
                    data: keywords
                }]
            };

            charts.keywordsChart.setOption(option);
        }

        async function updateNetworkChart() {
            const nodes = [
                {id: 1, name: '程序员', symbolSize: 50, category: 0},
                {id: 2, name: 'Python', symbolSize: 40, category: 1},
                {id: 3, name: '设计师', symbolSize: 45, category: 0},
                {id: 4, name: 'JavaScript', symbolSize: 35, category: 1},
                {id: 5, name: '产品经理', symbolSize: 40, category: 0},
                {id: 6, name: '数据分析', symbolSize: 30, category: 1}
            ];

            const links = [
                {source: 1, target: 2, value: 0.8},
                {source: 1, target: 4, value: 0.6},
                {source: 3, target: 4, value: 0.7},
                {source: 5, target: 6, value: 0.9}
            ];

            const categories = [
                {name: '职业'},
                {name: '技能'}
            ];

            const option = {
                title: {
                    text: '记忆关联网络',
                    left: 'center',
                    textStyle: { fontSize: 18, fontWeight: 'bold' }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function (params) {
                        if (params.dataType === 'edge') {
                            return `${params.data.source} → ${params.data.target}<br/>关联强度: ${params.data.value}`;
                        }
                        return params.name;
                    }
                },
                legend: {
                    data: categories.map(c => c.name),
                    orient: 'vertical',
                    left: 'left'
                },
                series: [{
                    type: 'graph',
                    layout: 'force',
                    data: nodes,
                    links: links,
                    categories: categories,
                    roam: true,
                    label: {
                        show: true,
                        position: 'right',
                        formatter: '{b}'
                    },
                    labelLayout: {
                        hideOverlap: true
                    },
                    scaleLimit: {
                        min: 0.4,
                        max: 2
                    },
                    lineStyle: {
                        color: 'source',
                        curveness: 0.3
                    },
                    emphasis: {
                        focus: 'adjacency',
                        lineStyle: {
                            width: 10
                        }
                    },
                    force: {
                        repulsion: 100,
                        gravity: 0.1,
                        edgeLength: 150,
                        layoutAnimation: true
                    }
                }]
            };

            charts.networkChart.setOption(option);
        }

        // 响应式图表处理
        window.addEventListener('resize', function() {
            for (const chartId in charts) {
                if (charts[chartId]) {
                    charts[chartId].resize();
                }
            }
        });

        // 性能图表更新函数
        async function updateResponseTimeChart() {
            try {
                const response = await fetch(`${API_BASE}/v1/analytics/performance`);
                const data = await response.json();

                const responseTimes = data.data.response_times || [];

                const option = {
                    title: {
                        text: 'API响应时间趋势',
                        left: 'center',
                        textStyle: { fontSize: 18, fontWeight: 'bold' }
                    },
                    tooltip: {
                        trigger: 'axis'
                    },
                    xAxis: {
                        type: 'category',
                        data: responseTimes.map((item, index) => `请求${index + 1}`)
                    },
                    yAxis: {
                        type: 'value',
                        name: '响应时间(秒)'
                    },
                    series: [{
                        name: '响应时间',
                        type: 'line',
                        data: responseTimes.map(item => item.duration),
                        smooth: true,
                        itemStyle: {
                            color: '#fa8c16'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: 'rgba(250, 140, 22, 0.3)'},
                                {offset: 1, color: 'rgba(250, 140, 22, 0.1)'}
                            ])
                        }
                    }]
                };

                charts.responseTimeChart.setOption(option);
            } catch (error) {
                console.error('更新响应时间图表失败:', error);
            }
        }

        async function updateRequestFrequencyChart() {
            try {
                const response = await fetch(`${API_BASE}/v1/analytics/performance`);
                const data = await response.json();

                const frequency = data.data.request_frequency || [];

                const option = {
                    title: {
                        text: 'API请求频率统计',
                        left: 'center',
                        textStyle: { fontSize: 18, fontWeight: 'bold' }
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: { type: 'shadow' }
                    },
                    xAxis: {
                        type: 'category',
                        data: frequency.map(item => item.endpoint.replace('/v1/', ''))
                    },
                    yAxis: {
                        type: 'value',
                        name: '调用次数'
                    },
                    series: [{
                        name: '调用次数',
                        type: 'bar',
                        data: frequency.map(item => item.count),
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: '#fa8c16'},
                                {offset: 1, color: '#d46b08'}
                            ])
                        }
                    }]
                };

                charts.requestFrequencyChart.setOption(option);
            } catch (error) {
                console.error('更新请求频率图表失败:', error);
            }
        }

        async function updateErrorRateChart() {
            try {
                const response = await fetch(`${API_BASE}/v1/analytics/performance`);
                const data = await response.json();

                const errorRate = data.data.error_rate || 0;
                const successRate = 100 - errorRate;

                const option = {
                    title: {
                        text: '系统健康度监控',
                        left: 'center',
                        textStyle: { fontSize: 18, fontWeight: 'bold' }
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b}: {c}%'
                    },
                    series: [{
                        name: '系统状态',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        data: [
                            {value: successRate, name: '成功请求', itemStyle: {color: '#52c41a'}},
                            {value: errorRate, name: '失败请求', itemStyle: {color: '#ff4d4f'}}
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }]
                };

                charts.errorRateChart.setOption(option);
            } catch (error) {
                console.error('更新错误率图表失败:', error);
            }
        }

        async function updateCacheAnalysisChart() {
            try {
                const response = await fetch(`${API_BASE}/performance`);
                const data = await response.json();

                const cache = data.cache_performance;
                const hitRate = cache.hit_rate * 100;
                const missRate = 100 - hitRate;

                const option = {
                    title: {
                        text: '缓存性能分析',
                        left: 'center',
                        textStyle: { fontSize: 18, fontWeight: 'bold' }
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b}: {c}% ({d}%)'
                    },
                    series: [{
                        name: '缓存性能',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        center: ['50%', '60%'],
                        data: [
                            {
                                value: hitRate,
                                name: '缓存命中',
                                itemStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {offset: 0, color: '#4facfe'},
                                        {offset: 1, color: '#00f2fe'}
                                    ])
                                }
                            },
                            {
                                value: missRate,
                                name: '缓存未命中',
                                itemStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {offset: 0, color: '#ff9a9e'},
                                        {offset: 1, color: '#fecfef'}
                                    ])
                                }
                            }
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }]
                };

                charts.cacheAnalysisChart.setOption(option);
            } catch (error) {
                console.error('更新缓存分析图表失败:', error);
            }
        }

        // 更新任务进度
        function updateTaskProgress(taskId, progress, stage) {
            if (taskId === currentTaskId) {
                const progressFill = document.getElementById('asyncProgressFill');
                const progressText = document.getElementById('asyncProgressText');

                if (progressFill) {
                    progressFill.style.width = progress + '%';
                }

                if (progressText) {
                    progressText.textContent = `${stage} (${progress.toFixed(1)}%)`;
                }

                // 更新阶段状态
                updateStageStatus(progress, stage);

                console.log(`📊 任务进度: ${progress.toFixed(1)}% - ${stage}`);
            }
        }

        // 更新阶段状态
        function updateStageStatus(progress, stage) {
            // 重置所有阶段状态
            document.querySelectorAll('.async-stage').forEach(stageEl => {
                stageEl.classList.remove('active', 'completed');
            });

            // 根据进度和阶段更新状态
            if (progress >= 10) {
                document.getElementById('stage1')?.classList.add('completed');
            }
            if (progress >= 30) {
                document.getElementById('stage2')?.classList.add('completed');
            }
            if (progress >= 70) {
                document.getElementById('stage3')?.classList.add('completed');
            }
            if (progress >= 100) {
                document.getElementById('stage4')?.classList.add('completed');
            }

            // 设置当前活跃阶段
            if (stage.includes('开始') || stage.includes('分析')) {
                document.getElementById('stage1')?.classList.add('active');
            } else if (stage.includes('LLM')) {
                document.getElementById('stage2')?.classList.add('active');
            } else if (stage.includes('向量') || stage.includes('生成')) {
                document.getElementById('stage3')?.classList.add('active');
            } else if (stage.includes('存储') || stage.includes('完成')) {
                document.getElementById('stage4')?.classList.add('active');
            }
        }

        // 处理任务完成
        function handleTaskCompleted(taskId, result) {
            if (taskId === currentTaskId) {
                console.log('✅ 异步任务完成:', taskId);

                const resultsContent = document.getElementById('addResultsContent');

                let html = `<h3>✅ 异步处理完成</h3>`;

                if (result.content_analysis) {
                    const analysis = result.content_analysis;
                    html += `<div class="memory-item">`;
                    html += `<div class="memory-text">内容分析</div>`;
                    html += `<div class="memory-meta">`;
                    html += `<span class="content-type-tag type-${analysis.type}">${analysis.type}</span>`;
                    html += `记忆片段: ${analysis.memory_count} 个 | `;
                    html += `关键词: ${analysis.keywords.slice(0, 3).join(', ')}`;
                    html += `</div></div>`;
                }

                if (result.results) {
                    html += '<h4>提取的记忆片段:</h4>';
                    result.results.forEach((item, index) => {
                        html += `<div class="memory-item">`;
                        html += `<div class="memory-text">${index + 1}. ${item.memory}</div>`;
                        html += `<div class="memory-meta">ID: ${item.id}</div>`;
                        html += `</div>`;
                    });
                }

                resultsContent.innerHTML = html;

                // 刷新统计
                refreshStats();

                // 清理任务ID
                currentTaskId = null;
            }
        }

        // 处理任务失败
        function handleTaskFailed(taskId, error) {
            if (taskId === currentTaskId) {
                console.error('❌ 异步任务失败:', taskId, error);

                const resultsContent = document.getElementById('addResultsContent');
                resultsContent.innerHTML = `
                    <h3>❌ 异步处理失败</h3>
                    <div class="memory-item">
                        <div class="memory-text">错误信息: ${error}</div>
                        <div class="memory-meta">任务ID: ${taskId}</div>
                    </div>
                    <p style="color: #666; margin-top: 15px;">
                        系统将自动回退到同步处理模式，请重试。
                    </p>
                `;

                // 清理任务ID
                currentTaskId = null;

                // 禁用异步处理
                asyncEnabled = false;
            }
        }

        // 自动刷新统计信息
        setInterval(refreshStats, 30000); // 每30秒刷新一次
    </script>
</body>
</html>
