#!/usr/bin/env python3
"""
mem0-m DeepSeek服务器
基于DeepSeek + Ollama的完整记忆系统服务器
"""
import logging
import os
import hashlib
import asyncio
from functools import lru_cache
from typing import Any, Dict, List, Optional
import uvicorn
from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse, HTMLResponse, FileResponse, RedirectResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
import base64
import mimetypes

from mem0 import Memory

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# 增强的性能监控和缓存类
class PerformanceOptimizer:
    def __init__(self):
        self.memory_cache = {}
        self.embedding_cache = {}  # 向量缓存
        self.cache_hits = 0
        self.cache_misses = 0
        self.api_metrics = []  # 存储API调用指标
        self.user_activities = {}  # 用户活动统计

        # 增强缓存配置
        self.max_cache_size = 1000  # 增加缓存大小
        self.cache_access_order = []  # LRU实现
        self.embedding_cache_size = 500  # 向量缓存大小

    def get_content_hash(self, content: str) -> str:
        """生成内容的哈希值用于缓存"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()

    def get_cached_memory(self, content_hash: str):
        """获取缓存的记忆结果"""
        if content_hash in self.memory_cache:
            self.cache_hits += 1
            logger.info(f"🎯 缓存命中: {content_hash[:8]}... (命中率: {self.get_cache_hit_rate():.1%})")
            return self.memory_cache[content_hash]
        self.cache_misses += 1
        return None

    def cache_memory_result(self, content_hash: str, result):
        """缓存记忆结果 - 增强LRU版本"""
        # LRU缓存管理
        if content_hash in self.memory_cache:
            # 更新访问顺序
            self.cache_access_order.remove(content_hash)
        elif len(self.memory_cache) >= self.max_cache_size:
            # 删除最久未使用的项
            oldest_key = self.cache_access_order.pop(0)
            del self.memory_cache[oldest_key]

        self.memory_cache[content_hash] = result
        self.cache_access_order.append(content_hash)

    def cache_embedding_result(self, text_hash: str, embedding: list):
        """缓存向量结果"""
        if text_hash in self.embedding_cache:
            return
        elif len(self.embedding_cache) >= self.embedding_cache_size:
            # 简单FIFO清理
            oldest_key = next(iter(self.embedding_cache))
            del self.embedding_cache[oldest_key]

        self.embedding_cache[text_hash] = {
            "embedding": embedding,
            "timestamp": time.time()
        }

    def get_cached_embedding(self, text_hash: str):
        """获取缓存的向量"""
        cached = self.embedding_cache.get(text_hash)
        if cached:
            logger.info(f"🎯 向量缓存命中: {text_hash[:8]}...")
            return cached["embedding"]
        return None

    def get_cache_hit_rate(self) -> float:
        """获取缓存命中率"""
        total = self.cache_hits + self.cache_misses
        return self.cache_hits / total if total > 0 else 0.0

    def get_cache_stats(self) -> dict:
        """获取缓存统计信息"""
        return {
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "hit_rate": self.get_cache_hit_rate(),
            "cached_items": len(self.memory_cache),
            "embedding_cached_items": len(self.embedding_cache),
            "cache_size_limit": self.max_cache_size,
            "embedding_cache_size_limit": self.embedding_cache_size
        }

    def record_api_call(self, endpoint: str, method: str, duration: float,
                       user_id: str, status: str, memory_count: int = 0):
        """记录API调用指标"""
        from datetime import datetime

        metric = {
            "timestamp": datetime.now().isoformat(),
            "endpoint": endpoint,
            "method": method,
            "duration": duration,
            "user_id": user_id,
            "status": status,
            "memory_count": memory_count
        }

        self.api_metrics.append(metric)

        # 限制存储数量，只保留最近1000条记录
        if len(self.api_metrics) > 1000:
            self.api_metrics = self.api_metrics[-1000:]

        # 更新用户活动统计
        if user_id:
            if user_id not in self.user_activities:
                self.user_activities[user_id] = {
                    "total_requests": 0,
                    "total_memories": 0,
                    "avg_response_time": 0,
                    "last_activity": None
                }

            activity = self.user_activities[user_id]
            activity["total_requests"] += 1
            activity["total_memories"] += memory_count
            activity["avg_response_time"] = (
                (activity["avg_response_time"] * (activity["total_requests"] - 1) + duration)
                / activity["total_requests"]
            )
            activity["last_activity"] = datetime.now().isoformat()

    def get_performance_analytics(self) -> dict:
        """获取性能分析数据"""
        if not self.api_metrics:
            return {
                "response_times": [],
                "request_frequency": [],
                "error_rate": 0,
                "user_activities": [],
                "memory_types": []
            }

        # 计算响应时间趋势
        response_times = [
            {"time": m["timestamp"], "duration": m["duration"]}
            for m in self.api_metrics[-50:]  # 最近50条记录
        ]

        # 计算请求频率
        from collections import Counter
        endpoint_counts = Counter(m["endpoint"] for m in self.api_metrics)
        request_frequency = [
            {"endpoint": endpoint, "count": count}
            for endpoint, count in endpoint_counts.most_common(10)
        ]

        # 计算错误率
        total_requests = len(self.api_metrics)
        error_requests = len([m for m in self.api_metrics if m["status"] == "error"])
        error_rate = (error_requests / total_requests * 100) if total_requests > 0 else 0

        # 用户活动数据
        user_activities = [
            {
                "user_id": user_id,
                "total_requests": data["total_requests"],
                "total_memories": data["total_memories"],
                "avg_response_time": round(data["avg_response_time"], 2),
                "last_activity": data["last_activity"]
            }
            for user_id, data in self.user_activities.items()
        ]

        return {
            "response_times": response_times,
            "request_frequency": request_frequency,
            "error_rate": round(error_rate, 2),
            "user_activities": user_activities,
            "total_api_calls": total_requests
        }

# 内容类型识别器
class ContentAnalyzer:
    @staticmethod
    def detect_content_type(content: str, filename: str = None) -> str:
        """检测内容类型"""
        content = content.strip()

        # 文件类型检测（基于文件名）
        if filename:
            mime_type, _ = mimetypes.guess_type(filename)
            if mime_type:
                if mime_type.startswith('image/'):
                    return 'image'
                elif mime_type.startswith('audio/'):
                    return 'audio'
                elif mime_type.startswith('video/'):
                    return 'video'
                elif mime_type in ['application/pdf']:
                    return 'pdf'
                elif mime_type in ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
                    return 'document'
                elif filename.endswith(('.py', '.js', '.html', '.css', '.java', '.cpp', '.c', '.go', '.rs')):
                    return 'code'

        # URL检测
        if content.startswith(('http://', 'https://', 'www.')):
            return 'url'

        # 代码检测
        if content.startswith('```') or content.count('\n') > 5 and any(
            keyword in content.lower() for keyword in
            ['def ', 'function', 'class ', 'import ', 'from ', 'var ', 'let ', 'const ']
        ):
            return 'code'

        # JSON检测
        if content.startswith(('{', '[')) and content.endswith(('}', ']')):
            try:
                import json
                json.loads(content)
                return 'json'
            except:
                pass

        # 长文本检测
        if len(content.split()) > 50:
            return 'long_text'

        # 短文本（默认）
        return 'short_text'

    @staticmethod
    def extract_keywords(content: str) -> list:
        """提取关键词"""
        import re

        # 简单的关键词提取
        words = re.findall(r'\b[a-zA-Z\u4e00-\u9fff]{2,}\b', content)

        # 过滤常见停用词
        stop_words = {'是', '的', '了', '在', '和', '有', '我', '你', '他', '她', 'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        keywords = [word for word in words if word.lower() not in stop_words]

        # 返回前10个关键词
        return list(set(keywords))[:10]

# 创建性能优化器和内容分析器实例
optimizer = PerformanceOptimizer()
content_analyzer = ContentAnalyzer()

# DeepSeek + Ollama 配置
DEEPSEEK_CONFIG = {
    "version": "v1.1",
    "llm": {
        "provider": "deepseek",
        "config": {
            "model": "deepseek-chat",
            "api_key": "***********************************",
            "deepseek_base_url": "https://api.deepseek.com",
            "temperature": 0.01,        # 进一步降低随机性，提升速度
            "max_tokens": 500,          # 大幅减少token数，提升响应速度
            "top_p": 0.9               # 优化采样参数
        }
    },
    "embedder": {
        "provider": "ollama", 
        "config": {
            "model": "bge-m3",
            "ollama_base_url": "http://localhost:11434",
            "embedding_dims": 1024
        }
    },
    "vector_store": {
        "provider": "chroma",
        "config": {
            "collection_name": "mem0_deepseek",
            "path": "./chroma_db"
        }
    }
}

# 初始化Memory实例
logger.info("🚀 初始化mem0 Memory实例...")
memory = Memory.from_config(DEEPSEEK_CONFIG)
logger.info("✅ Memory实例初始化成功")

# 创建FastAPI应用
app = FastAPI(
    title="mem0-m DeepSeek API",
    description="基于DeepSeek + Ollama的个人记忆系统API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic模型
class AddMemoryRequest(BaseModel):
    messages: List[Dict[str, str]] = Field(..., description="对话消息列表")
    user_id: Optional[str] = Field(None, description="用户ID")
    agent_id: Optional[str] = Field(None, description="代理ID")
    run_id: Optional[str] = Field(None, description="运行ID")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")
    filters: Optional[Dict[str, Any]] = Field(None, description="过滤器")

class SearchMemoryRequest(BaseModel):
    query: str = Field(..., description="搜索查询")
    user_id: Optional[str] = Field(None, description="用户ID")
    agent_id: Optional[str] = Field(None, description="代理ID")
    run_id: Optional[str] = Field(None, description="运行ID")
    limit: Optional[int] = Field(100, description="返回结果数量限制")
    filters: Optional[Dict[str, Any]] = Field(None, description="过滤器")

class UpdateMemoryRequest(BaseModel):
    memory_id: str = Field(..., description="记忆ID")
    data: str = Field(..., description="新的记忆内容")

class DeleteMemoryRequest(BaseModel):
    memory_id: str = Field(..., description="要删除的记忆ID")

# API端点
@app.get("/")
async def root():
    """根端点"""
    return {
        "message": "mem0-m DeepSeek API",
        "version": "1.0.0",
        "status": "running",
        "config": {
            "llm": "DeepSeek (deepseek-chat)",
            "embedder": "Ollama (bge-m3)",
            "vector_store": "ChromaDB (local)"
        },
        "dashboard": "http://localhost:8000/dashboard"
    }

@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard():
    """主仪表盘页面 - 重定向到分页式仪表盘"""
    return RedirectResponse(url="/paged-dashboard", status_code=301)

@app.get("/paged-dashboard", response_class=HTMLResponse)
async def paged_dashboard():
    """分页式仪表盘页面"""
    try:
        with open("paged_dashboard.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="分页式仪表盘页面未找到")

@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 简单的健康检查
        return {
            "status": "healthy",
            "timestamp": "2025-01-26T02:00:00Z",
            "components": {
                "memory": "ok",
                "llm": "ok",
                "embedder": "ok",
                "vector_store": "ok"
            },
            "performance": optimizer.get_cache_stats()
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")

@app.get("/performance")
async def get_performance_stats():
    """获取性能统计信息"""
    try:
        stats = optimizer.get_cache_stats()
        return {
            "cache_performance": stats,
            "optimization_status": "enabled",
            "features": {
                "memory_caching": True,
                "parameter_optimization": True,
                "performance_monitoring": True
            }
        }
    except Exception as e:
        logger.error(f"获取性能统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取性能统计失败: {str(e)}")

@app.get("/v1/analytics/performance")
async def get_performance_analytics():
    """获取详细的性能分析数据"""
    try:
        analytics = optimizer.get_performance_analytics()
        return {
            "status": "success",
            "data": analytics,
            "timestamp": "2025-01-26T16:00:00Z"
        }
    except Exception as e:
        logger.error(f"获取性能分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取性能分析失败: {str(e)}")

@app.get("/v1/analytics/memory-types")
async def get_memory_types_distribution():
    """获取记忆类型分布"""
    try:
        # 这里应该从数据库获取实际数据，现在返回模拟数据
        distribution = {
            "short_text": 45,
            "long_text": 25,
            "code": 15,
            "url": 10,
            "json": 5
        }

        return {
            "status": "success",
            "data": [
                {"type": type_name, "count": count, "percentage": round(count/sum(distribution.values())*100, 1)}
                for type_name, count in distribution.items()
            ]
        }
    except Exception as e:
        logger.error(f"获取记忆类型分布失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取记忆类型分布失败: {str(e)}")

@app.get("/v1/analytics/performance-detailed")
async def get_detailed_performance_analytics():
    """获取详细的性能分析数据"""
    try:
        analytics = optimizer.get_performance_analytics()
        cache_stats = optimizer.get_cache_stats()

        # 计算性能指标
        response_times = analytics.get("response_times", [])
        avg_response_time = sum(item["duration"] for item in response_times) / len(response_times) if response_times else 0

        # 性能等级评估
        performance_grade = "优秀" if avg_response_time < 5 else "良好" if avg_response_time < 15 else "需要优化"

        detailed_analytics = {
            **analytics,
            "cache_performance": cache_stats,
            "performance_metrics": {
                "avg_response_time": round(avg_response_time, 2),
                "performance_grade": performance_grade,
                "total_cache_items": cache_stats["cached_items"] + cache_stats["embedding_cached_items"],
                "cache_efficiency": cache_stats["hit_rate"] * 100
            },
            "optimization_suggestions": get_optimization_suggestions(avg_response_time, cache_stats["hit_rate"])
        }

        return {
            "status": "success",
            "data": detailed_analytics,
            "timestamp": "2025-01-26T16:00:00Z"
        }
    except Exception as e:
        logger.error(f"获取详细性能分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取详细性能分析失败: {str(e)}")

def get_optimization_suggestions(avg_response_time: float, cache_hit_rate: float) -> list:
    """获取优化建议"""
    suggestions = []

    if avg_response_time > 15:
        suggestions.append("响应时间较长，建议优化LLM参数或使用本地模型")

    if cache_hit_rate < 0.3:
        suggestions.append("缓存命中率较低，建议增加缓存大小或优化缓存策略")

    if avg_response_time > 10 and cache_hit_rate > 0.7:
        suggestions.append("缓存表现良好，但响应时间仍需优化，建议使用并行处理")

    if not suggestions:
        suggestions.append("系统性能表现良好，继续保持")

    return suggestions

@app.post("/v1/memories/upload")
async def upload_memory_file(
    file: UploadFile = File(...),
    user_id: str = Form(...),
    description: str = Form(default="")
):
    """上传文件并添加到记忆"""
    import time
    start_time = time.time()

    try:
        logger.info(f"⚡ 文件上传请求: filename={file.filename}, user_id={user_id}")

        # 读取文件内容
        file_content = await file.read()

        # 检测文件类型
        content_type = content_analyzer.detect_content_type("", file.filename)

        # 构建记忆内容
        if content_type == 'image':
            # 图片文件转换为base64
            base64_content = base64.b64encode(file_content).decode('utf-8')
            memory_content = f"上传了图片文件: {file.filename}"
            if description:
                memory_content += f"\n描述: {description}"
            memory_content += f"\n[图片数据: data:{file.content_type};base64,{base64_content[:100]}...]"

        elif content_type in ['audio', 'video']:
            # 音视频文件
            memory_content = f"上传了{content_type}文件: {file.filename}"
            if description:
                memory_content += f"\n描述: {description}"
            memory_content += f"\n文件大小: {len(file_content)} 字节"

        elif content_type == 'code':
            # 代码文件
            try:
                code_content = file_content.decode('utf-8')
                memory_content = f"上传了代码文件: {file.filename}\n"
                if description:
                    memory_content += f"描述: {description}\n"
                memory_content += f"代码内容:\n```\n{code_content}\n```"
            except UnicodeDecodeError:
                memory_content = f"上传了二进制代码文件: {file.filename}"
                if description:
                    memory_content += f"\n描述: {description}"

        elif content_type in ['document', 'pdf']:
            # 文档文件
            memory_content = f"上传了文档文件: {file.filename}"
            if description:
                memory_content += f"\n描述: {description}"
            memory_content += f"\n文件大小: {len(file_content)} 字节"

        else:
            # 文本文件或其他
            try:
                text_content = file_content.decode('utf-8')
                memory_content = f"上传了文件: {file.filename}\n"
                if description:
                    memory_content += f"描述: {description}\n"
                memory_content += f"内容:\n{text_content}"
            except UnicodeDecodeError:
                memory_content = f"上传了二进制文件: {file.filename}"
                if description:
                    memory_content += f"\n描述: {description}"
                memory_content += f"\n文件大小: {len(file_content)} 字节"

        # 添加到记忆系统
        messages = [
            {"role": "user", "content": memory_content},
            {"role": "assistant", "content": f"我已经记住了您上传的文件 {file.filename}。"}
        ]

        result = memory.add(messages, user_id=user_id)

        processing_time = time.time() - start_time
        memory_count = len(result.get('results', []))

        # 记录API调用指标
        optimizer.record_api_call(
            endpoint="/v1/memories/upload",
            method="POST",
            duration=processing_time,
            user_id=user_id,
            status="success",
            memory_count=memory_count
        )

        # 分析内容
        keywords = content_analyzer.extract_keywords(memory_content)

        # 增强返回结果
        enhanced_result = {
            **result,
            "processing_time": round(processing_time, 2),
            "file_info": {
                "filename": file.filename,
                "content_type": content_type,
                "file_size": len(file_content),
                "mime_type": file.content_type
            },
            "content_analysis": {
                "type": content_type,
                "keywords": keywords,
                "memory_count": memory_count
            }
        }

        logger.info(f"✅ 文件上传成功: {file.filename}, 类型: {content_type}")
        logger.info(f"⏱️  总处理时间: {processing_time:.2f}秒")
        logger.info(f"📊 提取记忆: {memory_count} 个")

        return enhanced_result

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"❌ 文件上传失败 ({processing_time:.2f}秒): {e}")

        # 记录失败的API调用
        optimizer.record_api_call(
            endpoint="/v1/memories/upload",
            method="POST",
            duration=processing_time,
            user_id=user_id,
            status="error",
            memory_count=0
        )

        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

@app.post("/v1/memories/batch")
async def add_memories_batch(request: AddMemoryRequest):
    """批量添加记忆 - 并行处理优化版本"""
    import time
    start_time = time.time()

    try:
        logger.info(f"⚡ 批量记忆添加请求: user_id={request.user_id}, messages={len(request.messages)}")

        # 检查缓存
        content_hash = hashlib.md5(str(request.messages).encode()).hexdigest()
        cached_result = optimizer.get_cached_memory(content_hash)
        if cached_result:
            return cached_result

        # 并行处理多个消息
        if len(request.messages) > 2:
            # 将消息分组并行处理
            tasks = []
            batch_size = 2  # 每批处理2个消息

            for i in range(0, len(request.messages), batch_size):
                batch_messages = request.messages[i:i+batch_size]
                batch_request = AddMemoryRequest(
                    messages=batch_messages,
                    user_id=request.user_id
                )
                task = asyncio.create_task(process_memory_batch(batch_request))
                tasks.append(task)

            # 等待所有任务完成
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)

            # 合并结果
            all_results = []
            for result in batch_results:
                if isinstance(result, dict) and 'results' in result:
                    all_results.extend(result['results'])

            final_result = {
                "results": all_results,
                "message": f"批量处理完成，共处理 {len(all_results)} 个记忆"
            }
        else:
            # 单个处理
            final_result = memory.add(request.messages, user_id=request.user_id)

        # 缓存结果
        optimizer.cache_memory_result(content_hash, final_result)

        processing_time = time.time() - start_time
        memory_count = len(final_result.get('results', []))

        # 记录API调用指标
        optimizer.record_api_call(
            endpoint="/v1/memories/batch",
            method="POST",
            duration=processing_time,
            user_id=request.user_id,
            status="success",
            memory_count=memory_count
        )

        logger.info(f"✅ 批量记忆添加成功: {memory_count} 个记忆")
        logger.info(f"⏱️  总处理时间: {processing_time:.2f}秒")

        return final_result

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"❌ 批量记忆添加失败 ({processing_time:.2f}秒): {e}")

        # 记录失败的API调用
        optimizer.record_api_call(
            endpoint="/v1/memories/batch",
            method="POST",
            duration=processing_time,
            user_id=request.user_id,
            status="error",
            memory_count=0
        )

        raise HTTPException(status_code=500, detail=f"批量记忆添加失败: {str(e)}")

async def process_memory_batch(request: AddMemoryRequest):
    """处理单个记忆批次"""
    try:
        result = memory.add(request.messages, user_id=request.user_id)
        return result
    except Exception as e:
        logger.error(f"批次处理失败: {e}")
        return {"results": [], "error": str(e)}

@app.post("/v1/memories")
async def add_memory(request: AddMemoryRequest):
    """添加记忆 - 优化版本"""
    import time
    start_time = time.time()

    try:
        logger.info(f"⚡ 添加记忆请求: user_id={request.user_id}")

        # 生成内容哈希用于缓存
        content_str = str(request.messages)
        content_hash = optimizer.get_content_hash(content_str)

        # 检查缓存
        cached_result = optimizer.get_cached_memory(content_hash)
        if cached_result:
            processing_time = time.time() - start_time
            logger.info(f"🚀 缓存命中，处理时间: {processing_time:.2f}秒")
            return cached_result

        # 构建参数字典，只传递非None的参数
        add_params = {"messages": request.messages}
        if request.user_id is not None:
            add_params["user_id"] = request.user_id
        if request.agent_id is not None:
            add_params["agent_id"] = request.agent_id
        if request.run_id is not None:
            add_params["run_id"] = request.run_id
        if request.metadata is not None:
            add_params["metadata"] = request.metadata

        # 执行记忆添加
        llm_start = time.time()
        result = memory.add(**add_params)
        llm_time = time.time() - llm_start

        # 缓存结果
        optimizer.cache_memory_result(content_hash, result)

        processing_time = time.time() - start_time
        memory_count = len(result.get('results', []))

        # 记录API调用指标
        optimizer.record_api_call(
            endpoint="/v1/memories",
            method="POST",
            duration=processing_time,
            user_id=request.user_id,
            status="success",
            memory_count=memory_count
        )

        # 分析记忆内容类型
        user_message_content = request.messages[0]['content'] if request.messages else ""
        content_type = content_analyzer.detect_content_type(user_message_content)
        keywords = content_analyzer.extract_keywords(user_message_content)

        # 增强返回结果
        enhanced_result = {
            **result,
            "processing_time": round(processing_time, 2),
            "content_analysis": {
                "type": content_type,
                "keywords": keywords,
                "memory_count": memory_count
            }
        }

        logger.info(f"✅ 记忆添加成功: {memory_count} 个记忆")
        logger.info(f"⏱️  总处理时间: {processing_time:.2f}秒 (LLM: {llm_time:.2f}秒)")
        logger.info(f"📊 内容类型: {content_type}, 关键词: {keywords[:3]}")
        logger.info(f"📊 缓存统计: {optimizer.get_cache_stats()}")

        return enhanced_result

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"❌ 添加记忆失败 ({processing_time:.2f}秒): {e}")
        raise HTTPException(status_code=500, detail=f"添加记忆失败: {str(e)}")

@app.post("/v1/memories/search")
async def search_memories(request: SearchMemoryRequest):
    """搜索记忆 - 优化版本"""
    import time
    start_time = time.time()

    try:
        logger.info(f"🔍 搜索记忆请求: query='{request.query}', user_id={request.user_id}")

        # 构建参数字典，只传递非None的参数
        search_params = {"query": request.query}
        if request.user_id is not None:
            search_params["user_id"] = request.user_id
        if request.agent_id is not None:
            search_params["agent_id"] = request.agent_id
        if request.run_id is not None:
            search_params["run_id"] = request.run_id
        if request.limit is not None:
            search_params["limit"] = request.limit

        result = memory.search(**search_params)

        processing_time = time.time() - start_time
        result_count = len(result.get('results', []))

        logger.info(f"✅ 搜索完成: 找到 {result_count} 个结果")
        logger.info(f"⏱️  搜索时间: {processing_time:.2f}秒")

        return result

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"❌ 搜索记忆失败 ({processing_time:.2f}秒): {e}")
        raise HTTPException(status_code=500, detail=f"搜索记忆失败: {str(e)}")

@app.get("/v1/memories")
async def get_all_memories(
    user_id: Optional[str] = None,
    agent_id: Optional[str] = None,
    run_id: Optional[str] = None,
    limit: Optional[int] = 100
):
    """获取所有记忆"""
    try:
        logger.info(f"获取所有记忆: user_id={user_id}, limit={limit}")
        
        # 构建参数字典，只传递非None的参数
        get_params = {}
        if user_id is not None:
            get_params["user_id"] = user_id
        if agent_id is not None:
            get_params["agent_id"] = agent_id
        if run_id is not None:
            get_params["run_id"] = run_id
        if limit is not None:
            get_params["limit"] = limit

        result = memory.get_all(**get_params)
        
        logger.info(f"获取记忆完成: {len(result.get('results', []))} 个记忆")
        return result
        
    except Exception as e:
        logger.error(f"获取记忆失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取记忆失败: {str(e)}")

@app.put("/v1/memories/{memory_id}")
async def update_memory(memory_id: str, request: UpdateMemoryRequest):
    """更新记忆"""
    try:
        logger.info(f"更新记忆: memory_id={memory_id}")
        
        result = memory.update(
            memory_id=memory_id,
            data=request.data
        )
        
        logger.info("记忆更新成功")
        return result
        
    except Exception as e:
        logger.error(f"更新记忆失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新记忆失败: {str(e)}")

@app.delete("/v1/memories/{memory_id}")
async def delete_memory(memory_id: str):
    """删除记忆"""
    try:
        logger.info(f"删除记忆: memory_id={memory_id}")
        
        result = memory.delete(memory_id=memory_id)
        
        logger.info("记忆删除成功")
        return result
        
    except Exception as e:
        logger.error(f"删除记忆失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除记忆失败: {str(e)}")

@app.delete("/v1/memories")
async def delete_all_memories(
    user_id: Optional[str] = None,
    agent_id: Optional[str] = None,
    run_id: Optional[str] = None
):
    """删除所有记忆"""
    try:
        logger.info(f"删除所有记忆: user_id={user_id}")
        
        # 构建参数字典，只传递非None的参数
        delete_params = {}
        if user_id is not None:
            delete_params["user_id"] = user_id
        if agent_id is not None:
            delete_params["agent_id"] = agent_id
        if run_id is not None:
            delete_params["run_id"] = run_id

        result = memory.delete_all(**delete_params)
        
        logger.info("所有记忆删除成功")
        return result
        
    except Exception as e:
        logger.error(f"删除所有记忆失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除所有记忆失败: {str(e)}")

@app.get("/v1/memories/{memory_id}/history")
async def get_memory_history(memory_id: str):
    """获取记忆历史"""
    try:
        logger.info(f"获取记忆历史: memory_id={memory_id}")
        
        result = memory.history(memory_id=memory_id)
        
        logger.info("记忆历史获取成功")
        return result
        
    except Exception as e:
        logger.error(f"获取记忆历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取记忆历史失败: {str(e)}")

def main():
    """启动服务器"""
    print("🚀 启动mem0-m DeepSeek服务器 (性能优化版)")
    print("=" * 70)
    print("🧠 mem0-m DeepSeek + Ollama 记忆系统")
    print("=" * 70)
    print("✅ 配置信息:")
    print("   • LLM: DeepSeek (deepseek-chat) - 优化参数")
    print("   • 嵌入模型: Ollama (bge-m3)")
    print("   • 向量数据库: ChromaDB (本地)")
    print("=" * 70)
    print("⚡ 性能优化:")
    print("   • 智能缓存: 启用记忆结果缓存")
    print("   • 参数优化: 降低温度和token数")
    print("   • 性能监控: 实时响应时间统计")
    print("=" * 70)
    print("🌐 服务地址:")
    print("   • API服务: http://localhost:8000")
    print("   • 📄 智能记忆仪表盘: http://localhost:8000/dashboard")
    print("   • 📄 分页式仪表盘: http://localhost:8000/paged-dashboard")
    print("   • API文档: http://localhost:8000/docs")
    print("   • 健康检查: http://localhost:8000/health")
    print("   • 性能统计: http://localhost:8000/performance")
    print("=" * 70)
    print("⚠️  按 Ctrl+C 停止服务")
    print("=" * 70)
    
    uvicorn.run(
        "deepseek_server:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )

if __name__ == "__main__":
    main()
